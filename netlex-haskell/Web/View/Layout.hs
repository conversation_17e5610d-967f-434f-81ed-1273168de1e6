module Web.View.Layout (defaultLayout, Html) where

import IHP.ViewPrelude
import IHP.Environment
import qualified Text.Blaze.Html5 as H
import qualified Text.Blaze.Html5.Attributes as A
import Application.Schema
import Web.Types
import Web.Routes

defaultLayout :: Html -> Html
defaultLayout inner = H.docTypeHtml ! A.lang "en" $ do
    H.head $ do
        H.meta ! A.charset "utf-8"
        H.meta ! A.name "viewport" ! A.content "width=device-width, initial-scale=1, shrink-to-fit=no"
        H.meta ! A.property "og:title" ! A.content "Netlex"
        H.meta ! A.property "og:type" ! A.content "website"
        H.meta ! A.property "og:url" ! A.content "https://netlex.com/"
        H.meta ! A.property "og:description" ! A.content "Modern web application built with IHP"
        
        H.title "Netlex"
        
        -- Bootstrap CSS
        H.link ! A.rel "stylesheet" ! A.href "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
        -- Font Awesome
        H.link ! A.rel "stylesheet" ! A.href "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        -- Custom CSS
        H.link ! A.rel "stylesheet" ! A.href "/static/app.css"
        
    H.body $ do
        navbar
        H.main ! A.class_ "container-fluid" $ do
            flashMessages
            inner
        footer
        
        -- Bootstrap JS
        H.script ! A.src "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" $ ""
        -- Custom JS
        H.script ! A.src "/static/app.js" $ ""

navbar :: Html
navbar = 
    H.nav ! A.class_ "navbar navbar-expand-lg navbar-dark bg-primary" $ do
        H.div ! A.class_ "container-fluid" $ do
            H.a ! A.class_ "navbar-brand" ! A.href "/" $ do
                H.i ! A.class_ "fas fa-cube me-2" $ ""
                "Netlex"
            
            H.button ! A.class_ "navbar-toggler" ! A.type_ "button" 
                     ! A.dataAttribute "bs-toggle" "collapse" 
                     ! A.dataAttribute "bs-target" "#navbarNav" $ do
                H.span ! A.class_ "navbar-toggler-icon" $ ""
            
            H.div ! A.class_ "collapse navbar-collapse" ! A.id "navbarNav" $ do
                H.ul ! A.class_ "navbar-nav me-auto" $ do
                    H.li ! A.class_ "nav-item" $ do
                        H.a ! A.class_ "nav-link" ! A.href "/" $ "Dashboard"
                    H.li ! A.class_ "nav-item" $ do
                        H.a ! A.class_ "nav-link" ! A.href "/Organizations" $ "Organizations"
                    H.li ! A.class_ "nav-item" $ do
                        H.a ! A.class_ "nav-link" ! A.href "/Projects" $ "Projects"
                    H.li ! A.class_ "nav-item" $ do
                        H.a ! A.class_ "nav-link" ! A.href "/ApiKeys" $ "API Keys"
                
                H.ul ! A.class_ "navbar-nav" $ do
                    userMenu

userMenu :: Html
userMenu = do
    H.li ! A.class_ "nav-item dropdown" $ do
        H.a ! A.class_ "nav-link dropdown-toggle" ! A.href "#" 
           ! A.id "navbarDropdown" ! A.role "button" 
           ! A.dataAttribute "bs-toggle" "dropdown" $ do
            H.i ! A.class_ "fas fa-user me-1" $ ""
            "Account"
        H.ul ! A.class_ "dropdown-menu" $ do
            H.li $ H.a ! A.class_ "dropdown-item" ! A.href "/Users" $ "Profile"
            H.li $ H.hr ! A.class_ "dropdown-divider"
            H.li $ H.a ! A.class_ "dropdown-item" ! A.href "/Sessions/delete" $ "Logout"

flashMessages :: Html
flashMessages = do
    H.div ! A.id "flash-messages" $ ""

footer :: Html
footer = 
    H.footer ! A.class_ "bg-light text-center text-lg-start mt-5" $ do
        H.div ! A.class_ "container p-4" $ do
            H.div ! A.class_ "row" $ do
                H.div ! A.class_ "col-lg-6 col-md-12 mb-4 mb-md-0" $ do
                    H.h5 ! A.class_ "text-uppercase" $ "Netlex"
                    H.p $ "Modern web application built with IHP (Integrated Haskell Platform)"
                
                H.div ! A.class_ "col-lg-3 col-md-6 mb-4 mb-md-0" $ do
                    H.h5 ! A.class_ "text-uppercase" $ "Links"
                    H.ul ! A.class_ "list-unstyled mb-0" $ do
                        H.li $ H.a ! A.href "/about" ! A.class_ "text-dark" $ "About"
                        H.li $ H.a ! A.href "/contact" ! A.class_ "text-dark" $ "Contact"
                
                H.div ! A.class_ "col-lg-3 col-md-6 mb-4 mb-md-0" $ do
                    H.h5 ! A.class_ "text-uppercase mb-0" $ "API"
                    H.ul ! A.class_ "list-unstyled" $ do
                        H.li $ H.a ! A.href "/api/health" ! A.class_ "text-dark" $ "Health Check"
                        H.li $ H.a ! A.href "/api/version" ! A.class_ "text-dark" $ "Version"
        
        H.div ! A.class_ "text-center p-3 bg-primary text-white" $ do
            "© 2025 Netlex. Built with "
            H.a ! A.class_ "text-white" ! A.href "https://ihp.digitallyinduced.com/" $ "IHP"
