# MySQL to PostgreSQL Migration Recommendations for Netlex3

This document provides comprehensive recommendations for migrating the Netlex3 application from MySQL to PostgreSQL, including step-by-step procedures, data validation strategies, and rollback procedures.

## Pre-Migration Assessment

### 1. Data Quality Analysis
Before migration, analyze the existing MySQL data for:

```sql
-- Check for orphaned records
SELECT COUNT(*) FROM archivio WHERE id_struttura NOT IN (SELECT id FROM strutture);
SELECT COUNT(*) FROM documento WHERE id_archivio NOT IN (SELECT id FROM archivio);

-- Check for invalid dates
SELECT COUNT(*) FROM archivio WHERE data_inizio > data_fine;
SELECT COUNT(*) FROM scadenzario WHERE data_scadenza < '2000-01-01';

-- Check for duplicate emails/usernames
SELECT email, COUNT(*) FROM utente GROUP BY email HAVING COUNT(*) > 1;
SELECT username, COUNT(*) FROM utente GROUP BY username HAVING COUNT(*) > 1;

-- Check for invalid VAT numbers
SELECT COUNT(*) FROM anagrafiche WHERE partita_iva IS NOT NULL 
  AND partita_iva NOT REGEXP '^[0-9]{11}$';

-- Check for empty required fields
SELECT COUNT(*) FROM archivio WHERE titolo = '' OR titolo IS NULL;
```

### 2. Schema Analysis
```sql
-- Get table sizes
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS "Size (MB)"
FROM information_schema.tables
WHERE table_schema = 'netlex3'
ORDER BY (data_length + index_length) DESC;

-- Get column data types
SELECT 
    table_name,
    column_name,
    data_type,
    column_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'netlex3'
ORDER BY table_name, ordinal_position;
```

## Migration Strategy

### Phase 1: Schema Creation (Week 1)

#### 1.1 Create PostgreSQL Schema
```bash
# Create database
createdb netlex3_postgres

# Run schema creation script
psql -d netlex3_postgres -f postgresql_schema.sql
```

#### 1.2 Create Migration Tables
```sql
-- Create mapping tables for ID conversion
CREATE TABLE migration_id_mapping (
    table_name VARCHAR(100),
    old_id INT,
    new_id UUID,
    organization_id UUID,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE migration_errors (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(100),
    old_id INT,
    error_message TEXT,
    error_data JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Phase 2: Organization Setup (Week 2)

#### 2.1 Create Organizations
```sql
-- Map MySQL strutture to PostgreSQL organizations
INSERT INTO organizations (id, name, legal_name, vat_number, tax_code, settings)
SELECT 
    gen_random_uuid(),
    nome,
    ragione_sociale,
    partita_iva,
    codice_fiscale,
    jsonb_build_object(
        'address', indirizzo,
        'city', citta,
        'province', provincia,
        'cap', cap,
        'phone', telefono,
        'email', email,
        'pec', pec
    )
FROM strutture;
```

#### 2.2 Create Organization Mapping
```sql
-- Store mapping for reference
INSERT INTO migration_id_mapping (table_name, old_id, new_id, organization_id)
SELECT 'organizations', s.id, o.id, o.id
FROM strutture s
JOIN organizations o ON o.name = s.nome;
```

### Phase 3: User Migration (Week 3)

#### 3.1 Migrate Users
```sql
-- Migrate users with organization mapping
INSERT INTO users (id, organization_id, username, email, password_hash, 
                   first_name, last_name, role, is_active, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    m.new_id as organization_id,
    u.username,
    u.email,
    u.password,
    u.nome,
    u.cognome,
    u.ruolo,
    u.attivo,
    u.data_creazione,
    u.data_aggiornamento
FROM utente u
JOIN migration_id_mapping m ON m.table_name = 'organizations' 
    AND m.old_id = u.id_struttura;
```

#### 3.2 Create User Mapping
```sql
INSERT INTO migration_id_mapping (table_name, old_id, new_id, organization_id)
SELECT 'users', u.id, new_users.id, new_users.organization_id
FROM utente u
JOIN users new_users ON new_users.username = u.username
JOIN migration_id_mapping m ON m.table_name = 'organizations' 
    AND m.old_id = u.id_struttura;
```

### Phase 4: Core Data Migration (Week 4-5)

#### 4.1 Migrate Anagrafiche
```sql
-- Migrate clients/contacts
INSERT INTO anagrafiche (id, organization_id, type, first_name, last_name, 
                        company_name, tax_code, vat_number, address, contact_info, 
                        is_active, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    m_org.new_id as organization_id,
    CASE WHEN tipo = 'persona' THEN 'person' ELSE 'company' END,
    nome,
    cognome,
    ragione_sociale,
    codice_fiscale,
    partita_iva,
    jsonb_build_object(
        'address', indirizzo,
        'city', citta,
        'province', provincia,
        'cap', cap
    ),
    jsonb_build_object(
        'phone', telefono,
        'email', email,
        'pec', pec
    ),
    attivo,
    data_creazione,
    data_aggiornamento
FROM anagrafiche a
JOIN migration_id_mapping m_org ON m_org.table_name = 'organizations' 
    AND m_org.old_id = a.id_struttura;

-- Create mapping
INSERT INTO migration_id_mapping (table_name, old_id, new_id, organization_id)
SELECT 'anagrafiche', a.id, new_anag.id, new_anag.organization_id
FROM anagrafiche a
JOIN anagrafiche new_anag ON new_anag.tax_code = a.codice_fiscale
JOIN migration_id_mapping m_org ON m_org.table_name = 'organizations' 
    AND m_org.old_id = a.id_struttura;
```

#### 4.2 Migrate Projects
```sql
-- Migrate projects (archivio)
INSERT INTO projects (id, organization_id, title, description, case_number, 
                     status_id, assigned_to, created_by, client_id, start_date, 
                     end_date, estimated_completion, budget, priority, is_active, 
                     created_at, updated_at)
SELECT 
    gen_random_uuid(),
    m_org.new_id as organization_id,
    titolo,
    descrizione,
    numero_pratica,
    (SELECT id FROM project_statuses WHERE name = stato),
    (SELECT new_id FROM migration_id_mapping WHERE table_name = 'users' 
     AND old_id = a.id_utente_assegnato),
    (SELECT new_id FROM migration_id_mapping WHERE table_name = 'users' 
     AND old_id = a.id_utente_creazione),
    (SELECT new_id FROM migration_id_mapping WHERE table_name = 'anagrafiche' 
     AND old_id = a.id_cliente),
    data_inizio,
    data_fine,
    data_stimata_completamento,
    budget,
    priorita,
    attivo,
    data_creazione,
    data_aggiornamento
FROM archivio a
JOIN migration_id_mapping m_org ON m_org.table_name = 'organizations' 
    AND m_org.old_id = a.id_struttura;

-- Create mapping
INSERT INTO migration_id_mapping (table_name, old_id, new_id, organization_id)
SELECT 'projects', a.id, new_proj.id, new_proj.organization_id
FROM archivio a
JOIN projects new_proj ON new_proj.case_number = a.numero_pratica
JOIN migration_id_mapping m_org ON m_org.table_name = 'organizations' 
    AND m_org.old_id = a.id_struttura;
```

### Phase 5: Related Data Migration (Week 6-7)

#### 5.1 Migrate Documents
```sql
-- Migrate documents
INSERT INTO documents (id, organization_id, project_id, uploaded_by, 
                      document_type, file_name, file_path, file_size, 
                      mime_type, protocol_number, protocol_date, 
                      category_id, tags, metadata, is_active, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    m_org.new_id as organization_id,
    m_proj.new_id as project_id,
    m_user.new_id as uploaded_by,
    tipo_documento,
    nome_file,
    percorso_file,
    dimensione_file,
    tipo_mime,
    numero_protocollo,
    data_protocollo,
    (SELECT id FROM document_categories WHERE name = categoria),
    string_to_array(tags, ','),
    jsonb_build_object(
        'description', descrizione,
        'keywords', parole_chiave
    ),
    attivo,
    data_creazione,
    data_aggiornamento
FROM documento d
JOIN migration_id_mapping m_org ON m_org.table_name = 'organizations' 
    AND m_org.old_id = d.id_struttura
JOIN migration_id_mapping m_proj ON m_proj.table_name = 'projects' 
    AND m_proj.old_id = d.id_archivio
LEFT JOIN migration_id_mapping m_user ON m_user.table_name = 'users' 
    AND m_user.old_id = d.id_utente;

-- Create mapping
INSERT INTO migration_id_mapping (table_name, old_id, new_id, organization_id)
SELECT 'documents', d.id, new_doc.id, new_doc.organization_id
FROM documento d
JOIN documents new_doc ON new_doc.file_name = d.nome_file
JOIN migration_id_mapping m_org ON m_org.table_name = 'organizations' 
    AND m_org.old_id = d.id_struttura;
```

#### 5.2 Migrate Timesheets
```sql
-- Migrate timesheets
INSERT INTO timesheets (id, organization_id, user_id, project_id, 
                       billing_id, date, hours_worked, description, 
                       hourly_rate, total_amount, is_billable, 
                       is_billed, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    m_org.new_id as organization_id,
    m_user.new_id as user_id,
    m_proj.new_id as project_id,
    m_bill.new_id as billing_id,
    data_lavoro,
    ore_lavorate,
    descrizione_attivita,
    tariffa_oraria,
    importo_totale,
    fatturabile,
    fatturato,
    data_creazione,
    data_aggiornamento
FROM timesheet t
JOIN migration_id_mapping m_org ON m_org.table_name = 'organizations' 
    AND m_org.old_id = t.id_struttura
JOIN migration_id_mapping m_user ON m_user.table_name = 'users' 
    AND m_user.old_id = t.id_utente
JOIN migration_id_mapping m_proj ON m_proj.table_name = 'projects' 
    AND m_proj.old_id = t.id_archivio
LEFT JOIN migration_id_mapping m_bill ON m_bill.table_name = 'billing' 
    AND m_bill.old_id = t.id_parcella;
```

### Phase 6: PCT Registry Migration (Week 8)

#### 6.1 Migrate PCT Registries
```sql
-- Migrate PCT registries (complex mapping)
INSERT INTO pct_registries (id, organization_id, project_id, created_by,
                           register_id, role_id, subject_id, rite_id, 
                           authority_id, act_id, case_number, court_code,
                           registry_date, protocol_number, parties, 
                           metadata, notes, is_active, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    m_org.new_id as organization_id,
    m_proj.new_id as project_id,
    m_user.new_id as created_by,
    (SELECT id FROM pct_registers WHERE code = r.registro),
    (SELECT id FROM pct_roles WHERE code = r.ruolo),
    (SELECT id FROM pct_subjects WHERE code = r.soggetto),
    (SELECT id FROM pct_rites WHERE code = r.rito),
    (SELECT id FROM pct_authorities WHERE name = r.autorita),
    (SELECT id FROM pct_acts WHERE unique_id = r.atto),
    r.numero_istanza,
    r.codice_tribunale,
    r.data_registrazione,
    r.numero_protocollo,
    jsonb_build_object(
        'parties', r.parti,
        'lawyers', r.avvocati
    ),
    jsonb_build_object(
        'phase', r.fase,
        'status', r.stato
    ),
    r.note,
    r.attivo,
    r.data_creazione,
    r.data_aggiornamento
FROM pct_registries r
JOIN migration_id_mapping m_org ON m_org.table_name = 'organizations' 
    AND m_org.old_id = r.id_struttura
JOIN migration_id_mapping m_proj ON m_proj.table_name = 'projects' 
    AND m_proj.old_id = r.id_archivio
JOIN migration_id_mapping m_user ON m_user.table_name = 'users' 
    AND m_user.old_id = r.id_utente;
```

## Data Validation Strategy

### 1. Row Count Validation
```sql
-- Compare row counts between MySQL and PostgreSQL
SELECT table_name, 
       mysql_count,
       postgres_count,
       CASE WHEN mysql_count = postgres_count THEN 'OK' ELSE 'MISMATCH' END as status
FROM (
    SELECT 'users' as table_name, 
           (SELECT COUNT(*) FROM utente) as mysql_count,
           (SELECT COUNT(*) FROM users) as postgres_count
    UNION ALL
    SELECT 'projects', 
           (SELECT COUNT(*) FROM archivio), 
           (SELECT COUNT(*) FROM projects)
    -- Add more tables as needed
) counts;
```

### 2. Data Integrity Validation
```sql
-- Validate foreign key relationships
SELECT 'users' as table_name, COUNT(*) as invalid_refs
FROM users u
WHERE u.organization_id NOT IN (SELECT id FROM organizations)

UNION ALL

SELECT 'projects', COUNT(*)
FROM projects p
WHERE p.organization_id NOT IN (SELECT id FROM organizations);
```

### 3. Business Logic Validation
```sql
-- Validate critical business rules
-- Example: Ensure all active projects have valid dates
SELECT COUNT(*) as invalid_projects
FROM projects
WHERE is_active = true 
  AND end_date IS NOT NULL 
  AND end_date < start_date;
```

## Rollback Procedures

### 1. Immediate Rollback (within 24 hours)
```bash
# Stop application
sudo systemctl stop netlex3

# Restore from backup
pg_restore -d netlex3_postgres netlex3_backup_pre_migration.dump

# Revert application configuration
# Update database connection string to MySQL
```

### 2. Delayed Rollback (after 24 hours)
```bash
# Create rollback script to export new data
pg_dump -d netlex3_postgres --data-only --inserts > rollback_data.sql

# Restore MySQL backup
mysql netlex3 < netlex3_backup_pre_migration.sql

# Re-run MySQL migrations for any schema changes
```

## Performance Optimization

### 1. Index Creation
```sql
-- Create indexes after data migration for better performance
CREATE INDEX CONCURRENTLY idx_users_organization_id ON users(organization_id);
CREATE INDEX CONCURRENTLY idx_projects_organization_id ON projects(organization_id);
-- Add other indexes as needed
```

### 2. Vacuum and Analyze
```sql
-- Update statistics after migration
VACUUM ANALYZE;
```

## Testing Checklist

### 1. Functional Testing
- [ ] User authentication and authorization
- [ ] Project creation and management
- [ ] Document upload and retrieval
- [ ] Timesheet entry and billing
- [ ] PCT registry operations
- [ ] Report generation

### 2. Performance Testing
- [ ] Query performance benchmarks
- [ ] Concurrent user load testing
- [ ] Large dataset operations
- [ ] Backup and restore performance

### 3. Security Testing
- [ ] Multi-tenancy isolation
- [ ] Data access controls
- [ ] SQL injection prevention
- [ ] Audit trail verification

## Post-Migration Tasks

### 1. Monitoring Setup
```sql
-- Create monitoring queries
CREATE OR REPLACE VIEW migration_monitoring AS
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_rows,
    last_vacuum,
    last_autovacuum
FROM pg_stat_user_tables;
```

### 2. Backup Strategy
```bash
# Set up automated backups
# Daily backups
0 2 * * * pg_dump -d netlex3_postgres | gzip > /backups/netlex3_$(date +%Y%m%d).sql.gz

# Weekly full backups
0 3 * * 0 pg_dump -d netlex3_postgres --format=custom > /backups/netlex3_weekly_$(date +%Y%m%d).dump
```

### 3. Documentation Updates
- Update API documentation
- Update user manuals
- Update system administration guides
- Update disaster recovery procedures