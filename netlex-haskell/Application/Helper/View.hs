module Application.Helper.View where

import IHP.ViewPrelude
import Application.Schema
import qualified Text.Blaze.Html5 as H
import qualified Text.Blaze.Html5.Attributes as A

-- User display helpers
userDisplayName :: User -> Text
userDisplayName user = 
    case (firstName user, lastName user) of
        (Just first, Just last) -> first <> " " <> last
        (Just first, Nothing) -> first
        (Nothing, Just last) -> last
        (Nothing, Nothing) -> email user

userInitials :: User -> Text
userInitials user =
    case (firstName user, lastName user) of
        (Just first, Just last) -> 
            take 1 first <> take 1 last
        (Just first, Nothing) -> 
            take 2 first
        (Nothing, Just last) -> 
            take 2 last
        (Nothing, Nothing) -> 
            take 2 (email user)

-- Date formatting helpers
formatDate :: UTCTime -> Text
formatDate = cs . formatTime defaultTimeLocale "%Y-%m-%d"

formatDateTime :: UTCTime -> Text
formatDateTime = cs . formatTime defaultTimeLocale "%Y-%m-%d %H:%M:%S"

formatDateTimeShort :: UTCTime -> Text
formatDateTimeShort = cs . formatTime defaultTimeLocale "%m/%d/%Y %H:%M"

-- Status badge helpers
statusBadge :: Text -> Html
statusBadge status = 
    H.span ! A.class_ ("badge " <> badgeClass status) $ H.text status
  where
    badgeClass "active" = "badge-success"
    badgeClass "inactive" = "badge-secondary"
    badgeClass "pending" = "badge-warning"
    badgeClass "error" = "badge-danger"
    badgeClass _ = "badge-primary"

-- Pagination helpers
paginationNav :: PaginationInfo -> Text -> Html
paginationNav paginationInfo baseUrl = 
    H.nav ! A.ariaLabel "Page navigation" $ do
        H.ul ! A.class_ "pagination justify-content-center" $ do
            -- Previous button
            H.li ! A.class_ (if hasPrevPage paginationInfo then "page-item" else "page-item disabled") $ do
                if hasPrevPage paginationInfo
                    then H.a ! A.class_ "page-link" ! A.href (cs $ baseUrl <> "?page=" <> show (currentPage paginationInfo - 1)) $ "Previous"
                    else H.span ! A.class_ "page-link" $ "Previous"
            
            -- Page numbers
            mapM_ (pageLink baseUrl (currentPage paginationInfo)) [1..totalPages paginationInfo]
            
            -- Next button
            H.li ! A.class_ (if hasNextPage paginationInfo then "page-item" else "page-item disabled") $ do
                if hasNextPage paginationInfo
                    then H.a ! A.class_ "page-link" ! A.href (cs $ baseUrl <> "?page=" <> show (currentPage paginationInfo + 1)) $ "Next"
                    else H.span ! A.class_ "page-link" $ "Next"
  where
    pageLink baseUrl current page = 
        H.li ! A.class_ (if page == current then "page-item active" else "page-item") $ do
            if page == current
                then H.span ! A.class_ "page-link" $ H.text (show page)
                else H.a ! A.class_ "page-link" ! A.href (cs $ baseUrl <> "?page=" <> show page) $ H.text (show page)

-- Form helpers
formGroup :: Text -> Html -> Html
formGroup label input = 
    H.div ! A.class_ "form-group mb-3" $ do
        H.label ! A.class_ "form-label" $ H.text label
        input

textInput :: Text -> Text -> Maybe Text -> Html
textInput name value placeholder = 
    H.input 
        ! A.type_ "text"
        ! A.class_ "form-control"
        ! A.name (cs name)
        ! A.value (cs value)
        ! maybe mempty (A.placeholder . cs) placeholder

emailInput :: Text -> Text -> Html
emailInput name value = 
    H.input 
        ! A.type_ "email"
        ! A.class_ "form-control"
        ! A.name (cs name)
        ! A.value (cs value)
        ! A.required ""

passwordInput :: Text -> Html
passwordInput name = 
    H.input 
        ! A.type_ "password"
        ! A.class_ "form-control"
        ! A.name (cs name)
        ! A.required ""

textareaInput :: Text -> Text -> Maybe Text -> Html
textareaInput name value placeholder = 
    H.textarea 
        ! A.class_ "form-control"
        ! A.name (cs name)
        ! maybe mempty (A.placeholder . cs) placeholder
        $ H.text value

selectInput :: Text -> [(Text, Text)] -> Maybe Text -> Html
selectInput name options selectedValue = 
    H.select ! A.class_ "form-control" ! A.name (cs name) $ do
        mapM_ optionElement options
  where
    optionElement (value, label) = 
        H.option 
            ! A.value (cs value)
            ! (if Just value == selectedValue then A.selected "" else mempty)
            $ H.text label

-- Alert helpers
successAlert :: Text -> Html
successAlert message = 
    H.div ! A.class_ "alert alert-success alert-dismissible fade show" ! A.role "alert" $ do
        H.text message
        H.button ! A.type_ "button" ! A.class_ "btn-close" ! A.dataAttribute "bs-dismiss" "alert" $ mempty

errorAlert :: Text -> Html
errorAlert message = 
    H.div ! A.class_ "alert alert-danger alert-dismissible fade show" ! A.role "alert" $ do
        H.text message
        H.button ! A.type_ "button" ! A.class_ "btn-close" ! A.dataAttribute "bs-dismiss" "alert" $ mempty

warningAlert :: Text -> Html
warningAlert message = 
    H.div ! A.class_ "alert alert-warning alert-dismissible fade show" ! A.role "alert" $ do
        H.text message
        H.button ! A.type_ "button" ! A.class_ "btn-close" ! A.dataAttribute "bs-dismiss" "alert" $ mempty

infoAlert :: Text -> Html
infoAlert message = 
    H.div ! A.class_ "alert alert-info alert-dismissible fade show" ! A.role "alert" $ do
        H.text message
        H.button ! A.type_ "button" ! A.class_ "btn-close" ! A.dataAttribute "bs-dismiss" "alert" $ mempty
