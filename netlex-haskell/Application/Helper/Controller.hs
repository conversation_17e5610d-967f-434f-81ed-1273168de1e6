module Application.Helper.Controller where

import IHP.ControllerPrelude
import Application.Schema
import qualified IHP.AuthSupport.Controller.Sessions as Sessions

-- Authentication helpers
currentUser :: (?context :: ControllerContext) => IO (Maybe User)
currentUser = Sessions.getCurrentUser

currentUserOrRedirect :: (?context :: ControllerContext) => IO User
currentUserOrRedirect = do
    user <- currentUser
    case user of
        Just u -> pure u
        Nothing -> redirectTo "/login"

requireAdmin :: (?context :: ControllerContext) => IO User
requireAdmin = do
    user <- currentUserOrRedirect
    if isAdmin user
        then pure user
        else do
            setErrorMessage "Access denied. Admin privileges required."
            redirectTo "/"

-- Organization helpers
getCurrentUserOrganizations :: (?context :: ControllerContext) => User -> IO [Organization]
getCurrentUserOrganizations user = do
    userOrgs <- query @UserOrganization
        |> filterWhere (#userId, get #id user)
        |> fetch
    
    let orgIds = map (get #organizationId) userOrgs
    query @Organization
        |> filterWhereIn (#id, orgIds)
        |> fetch

requireOrganizationAccess :: (?context :: ControllerContext) => Id Organization -> IO User
requireOrganizationAccess orgId = do
    user <- currentUserOrRedirect
    hasAccess <- userHasOrganizationAccess user orgId
    if hasAccess
        then pure user
        else do
            setErrorMessage "Access denied. You don't have access to this organization."
            redirectTo "/"

userHasOrganizationAccess :: (?context :: ControllerContext) => User -> Id Organization -> IO Bool
userHasOrganizationAccess user orgId = do
    if isAdmin user
        then pure True
        else do
            userOrg <- query @UserOrganization
                |> filterWhere (#userId, get #id user)
                |> filterWhere (#organizationId, orgId)
                |> fetchOneOrNothing
            pure (isJust userOrg)

-- Audit logging
logAction :: (?context :: ControllerContext) => Text -> Maybe Text -> Maybe UUID -> Value -> IO ()
logAction action resourceType resourceId details = do
    user <- currentUser
    let userId = fmap (get #id) user
    
    -- Get IP address from request
    let ipAddress = Nothing -- TODO: Extract from request headers
    let userAgent = Nothing -- TODO: Extract from request headers
    
    newRecord @AuditLog
        |> set #userId userId
        |> set #action action
        |> set #resourceType resourceType
        |> set #resourceId resourceId
        |> set #details details
        |> set #ipAddress ipAddress
        |> set #userAgent userAgent
        |> createRecord
    
    pure ()

-- Pagination helpers
data PaginationInfo = PaginationInfo
    { currentPage :: Int
    , totalPages :: Int
    , totalItems :: Int
    , itemsPerPage :: Int
    , hasNextPage :: Bool
    , hasPrevPage :: Bool
    } deriving (Show, Eq)

paginate :: (?context :: ControllerContext) => Int -> QueryBuilder table -> IO ([table], PaginationInfo)
paginate itemsPerPage queryBuilder = do
    page <- param "page" |> fromMaybe 1
    let offset = (page - 1) * itemsPerPage
    
    -- Get total count
    totalItems <- queryBuilder |> fetchCount
    let totalPages = ceiling (fromIntegral totalItems / fromIntegral itemsPerPage)
    
    -- Get paginated results
    items <- queryBuilder
        |> limit itemsPerPage
        |> offset offset
        |> fetch
    
    let paginationInfo = PaginationInfo
            { currentPage = page
            , totalPages = totalPages
            , totalItems = totalItems
            , itemsPerPage = itemsPerPage
            , hasNextPage = page < totalPages
            , hasPrevPage = page > 1
            }
    
    pure (items, paginationInfo)

-- JSON response helpers
respondJSON :: (?context :: ControllerContext, ToJSON a) => a -> IO ()
respondJSON value = do
    setHeader "Content-Type" "application/json"
    renderPlain (cs $ encode value)

respondError :: (?context :: ControllerContext) => Int -> Text -> IO ()
respondError statusCode message = do
    setStatus statusCode
    respondJSON $ object ["error" .= message]

respondSuccess :: (?context :: ControllerContext, ToJSON a) => a -> IO ()
respondSuccess = respondJSON
