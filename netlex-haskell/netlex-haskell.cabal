cabal-version:       2.2
name:                netlex-haskell
version:             *******
synopsis:            Netlex Haskell application using IHP framework
description:         A modern web application built with IHP (Integrated Haskell Platform) to replace the Netlex3 PHP project
homepage:            https://github.com/netlex/netlex-haskell
license:             MIT
license-file:        LICENSE
author:              Netlex Team
maintainer:          <EMAIL>
copyright:           2025 Netlex
category:            Web
build-type:          Simple

common shared-properties
    default-language: Haskell2010
    build-depends:
        base >= 4.7 && < 5
      , ihp
      , classy-prelude
      , directory
      , string-conversions
      , wai
      , wai-extra
      , warp
      , http-types
      , inflections
      , text
      , hlint
      , wai-app-static
      , wai-util
      , aeson
      , uuid
      , time
      , attoparsec
      , ghc-prim
      , containers
      , random
      , bytestring
      , network-uri
      , uri-encode
      , blaze-html
      , blaze-markup
      , mmark
      , mmark-ext
      , interpolate
      , split
      , unix
      , fsnotify
      , countable-inflections
      , typerep-map
      , basic-prelude
      , data-default
      , regex-tdfa
      , resource-pool
      , wreq
      , deepseq
      , unordered-containers
      , scientific
      , vector
      , temporary
      , neat-interpolation
      , with-utf8
      , ihp-hsx
      , ihp-postgresql-simple-extra
      , postgresql-simple
      , load-env

    default-extensions:
        OverloadedStrings
      , NoImplicitPrelude
      , ImplicitParams
      , Rank2Types
      , NamedFieldPuns
      , TypeSynonymInstances
      , FlexibleInstances
      , DisambiguateRecordFields
      , DuplicateRecordFields
      , OverloadedLabels
      , FlexibleContexts
      , DataKinds
      , QuasiQuotes
      , TypeFamilies
      , PackageImports
      , ScopedTypeVariables
      , RecordWildCards
      , TypeApplications
      , DataKinds
      , InstanceSigs
      , DeriveGeneric
      , MultiParamTypeClasses
      , TypeOperators
      , DeriveDataTypeable
      , DefaultSignatures
      , BangPatterns
      , FunctionalDependencies
      , PartialTypeSignatures
      , BlockArguments
      , LambdaCase
      , StandaloneDeriving
      , TemplateHaskell

executable RunUnoptimizedProdServer
    import: shared-properties
    main-is: Main.hs
    hs-source-dirs: .
    ghc-options: -threaded -rtsopts -with-rtsopts=-N -O2

executable RunOptimizedProdServer  
    import: shared-properties
    main-is: Main.hs
    hs-source-dirs: .
    ghc-options: -threaded -rtsopts -with-rtsopts=-N -O2 -funfolding-use-threshold=16 -fexcess-precision -optc-O3 -optc-ffast-math

executable RunDevServer
    import: shared-properties
    main-is: Main.hs
    hs-source-dirs: .
    ghc-options: -threaded -rtsopts -with-rtsopts=-N

library
    import: shared-properties
    hs-source-dirs: .
    exposed-modules:
        Config
      , Application.Schema
      , Application.Script.Prelude
      , Application.Helper.Controller
      , Application.Helper.View
      , Web.Controller.Prelude
      , Web.View.Prelude
      , Web.View.Layout
      , Web.Routes
      , Web.FrontController
      , Web.Types
      , Admin.Routes
      , Admin.FrontController
      , Admin.Types
