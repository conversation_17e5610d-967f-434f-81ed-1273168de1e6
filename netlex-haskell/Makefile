.PHONY: build run dev clean test install db-setup db-migrate db-reset

# Default target
all: build

# Build the project
build:
	stack build

# Run the development server
dev:
	stack run RunDevServer

# Run the production server
run:
	stack run RunUnoptimizedProdServer

# Run the optimized production server
run-optimized:
	stack run RunOptimizedProdServer

# Clean build artifacts
clean:
	stack clean
	rm -rf .stack-work/

# Install dependencies
install:
	stack build --only-dependencies

# Database setup
db-setup:
	createdb netlex_haskell_dev || true
	createdb netlex_haskell_test || true

# Run database migrations
db-migrate:
	psql -d netlex_haskell_dev -f Application/Schema.sql

# Reset database
db-reset:
	dropdb netlex_haskell_dev || true
	dropdb netlex_haskell_test || true
	make db-setup
	make db-migrate

# Run tests
test:
	stack test

# Format code
format:
	find . -name "*.hs" -not -path "./.stack-work/*" -exec ormolu --mode inplace {} \;

# Lint code
lint:
	hlint .

# Generate documentation
docs:
	stack haddock

# Development setup
setup: install db-setup db-migrate
	@echo "Development environment setup complete!"
	@echo "Run 'make dev' to start the development server"

# Docker commands
docker-build:
	docker build -t netlex-haskell .

docker-run:
	docker run -p 8000:8000 netlex-haskell

# Help
help:
	@echo "Available commands:"
	@echo "  build         - Build the project"
	@echo "  dev           - Run development server"
	@echo "  run           - Run production server"
	@echo "  run-optimized - Run optimized production server"
	@echo "  clean         - Clean build artifacts"
	@echo "  install       - Install dependencies"
	@echo "  db-setup      - Create databases"
	@echo "  db-migrate    - Run database migrations"
	@echo "  db-reset      - Reset databases"
	@echo "  test          - Run tests"
	@echo "  format        - Format code with ormolu"
	@echo "  lint          - Lint code with hlint"
	@echo "  docs          - Generate documentation"
	@echo "  setup         - Complete development setup"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run Docker container"
	@echo "  help          - Show this help"
