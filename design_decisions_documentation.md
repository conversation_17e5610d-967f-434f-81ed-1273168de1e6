# Netlex3 PostgreSQL Schema Design Decisions

This document provides detailed explanations for the key design decisions made in the PostgreSQL schema migration from MySQL.

## 1. UUID Primary Keys vs Auto-Increment Integers

### Decision: Use UUID v4 for all primary keys

**Rationale:**
- **Scalability**: UUIDs allow for distributed database systems without coordination
- **Security**: Prevents enumeration attacks (can't guess sequential IDs)
- **Multi-tenancy**: Eliminates ID conflicts when merging data from different organizations
- **Migration**: Easier to maintain relationships during data migration from MySQL

**Implementation:**
```sql
id UUID PRIMARY KEY
```

**Trade-offs:**
- **Storage**: UUIDs use 16 bytes vs 4 bytes for integers (acceptable for modern storage)
- **Performance**: Slightly slower index lookups (mitigated by proper indexing)
- **Readability**: Less human-friendly than integers (addressed by using meaningful business keys)

## 2. Multi-Tenancy Architecture

### Decision: Row-level security through organization scoping

**Rationale:**
- **Isolation**: Each organization has completely separate data
- **Scalability**: Can easily add new organizations without schema changes
- **Compliance**: Meets data isolation requirements for legal software
- **Migration**: Simplifies migration from single-tenant MySQL to multi-tenant PostgreSQL

**Implementation:**
```sql
organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE
```

**Additional Considerations:**
- All queries must include `WHERE organization_id = ?`
- PostgreSQL Row Level Security (RLS) can be implemented for automatic filtering
- Indexes on organization_id for performance

## 3. JSONB vs Traditional Columns

### Decision: Use JSONB for flexible, semi-structured data

**Rationale:**
- **Flexibility**: Accommodates varying data structures without schema changes
- **Migration**: Handles MySQL text fields with inconsistent formats
- **Performance**: PostgreSQL JSONB supports indexing and efficient queries
- **Future-proofing**: Allows for custom fields per organization

**Implementation:**
```sql
address JSONB,
contact_info JSONB,
metadata JSONB,
settings JSONB
```

**Use Cases:**
- **Address**: Varying address formats (Italian vs international)
- **Contact Info**: Phone numbers, emails, social media handles
- **Metadata**: Custom project attributes
- **Settings**: Organization-specific configurations

## 4. Enhanced Data Validation

### Decision: Comprehensive CHECK constraints and data validation

**Rationale:**
- **Data Quality**: Prevents invalid data at the database level
- **Migration**: Identifies and cleans problematic data during migration
- **Business Rules**: Enforces legal and business requirements
- **Performance**: Reduces application-level validation overhead

**Examples:**
```sql
-- Email format validation
CONSTRAINT chk_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')

-- Date validation
CONSTRAINT chk_project_dates CHECK (end_date IS NULL OR start_date IS NULL OR end_date >= start_date)

-- Financial validation
CONSTRAINT chk_total_amount CHECK (total_amount = amount + tax_amount)
```

## 5. Time Zone Handling

### Decision: Use TIMESTAMP WITH TIME ZONE for all timestamps

**Rationale:**
- **Accuracy**: Preserves original time zone information
- **Migration**: Handles MySQL DATETIME fields correctly
- **Compliance**: Legal documents require precise timing
- **Reporting**: Enables accurate time-based reporting across time zones

**Implementation:**
```sql
created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
```

## 6. Financial Data Types

### Decision: Use DECIMAL for monetary values

**Rationale:**
- **Precision**: Avoids floating-point rounding errors
- **Compliance**: Meets financial reporting requirements
- **Migration**: Preserves exact values from MySQL DECIMAL fields
- **International**: Supports multiple currencies with consistent precision

**Implementation:**
```sql
amount DECIMAL(15,2) CHECK (amount >= 0)
```

## 7. Relationship Design Patterns

### Decision: Junction tables for many-to-many relationships

**Rationale:**
- **Flexibility**: Supports complex relationships with additional attributes
- **Migration**: Handles MySQL's implicit many-to-many relationships
- **Performance**: Enables efficient queries with proper indexing
- **Extensibility**: Easy to add relationship-specific attributes

**Example:**
```sql
CREATE TABLE project_anagrafiche (
    id UUID PRIMARY KEY,
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    anagrafica_id UUID NOT NULL REFERENCES anagrafiche(id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL,
    notes TEXT,
    UNIQUE(project_id, anagrafica_id, relationship_type)
);
```

## 8. PCT Registry Design

### Decision: Normalized structure for Processo Telematico data

**Rationale:**
- **Compliance**: Meets Italian legal system requirements
- **Flexibility**: Supports different registry types (civil, criminal, etc.)
- **Migration**: Handles complex PCT data from MySQL
- **Performance**: Optimized for common PCT queries

**Implementation:**
- Separate tables for registers, roles, subjects, rites, authorities
- Junction table for registry-act relationships
- Proper indexing for PCT-specific queries

## 9. Indexing Strategy

### Decision: Strategic indexing based on query patterns

**Rationale:**
- **Performance**: Optimizes for common query patterns
- **Migration**: Identifies performance bottlenecks in MySQL queries
- **Scalability**: Supports growing data volumes
- **Maintenance**: Balances query performance with write overhead

**Strategy:**
- Primary keys: Automatically indexed
- Foreign keys: Always indexed
- Common filters: Indexed (status, active flags, dates)
- Search fields: Indexed (names, case numbers, protocol numbers)

## 10. Soft Delete vs Hard Delete

### Decision: Use soft delete with is_active flags

**Rationale:**
- **Compliance**: Legal requirements for data retention
- **Recovery**: Ability to restore accidentally deleted data
- **Audit Trail**: Maintains historical records
- **Migration**: Preserves referential integrity during migration

**Implementation:**
```sql
is_active BOOLEAN DEFAULT true
```

**ON DELETE CASCADE** used for:
- Organization deletion (cascades to all related data)
- User deletion (cascades to created records)
- Project deletion (cascades to related documents, timesheets, etc.)

## 11. Naming Conventions

### Decision: English naming with Italian mapping

**Rationale:**
- **Standardization**: Consistent with PostgreSQL best practices
- **Migration**: Clear mapping from Italian MySQL names
- **Internationalization**: Easier for non-Italian developers
- **Documentation**: Self-documenting schema

**Mapping Examples:**
- `utente` → `users`
- `archivio` → `projects`
- `anagrafiche` → `anagrafiche` (kept for business significance)
- `documento` → `documents`
- `timesheet` → `timesheets`
- `parcella` → `billing`
- `scadenzario` → `deadlines`
- `agenda` → `appointments`

## 12. Data Migration Strategy

### Decision: Phased migration with data validation

**Rationale:**
- **Risk Mitigation**: Gradual migration reduces risk
- **Validation**: Identifies data quality issues early
- **Rollback**: Ability to revert if issues arise
- **Testing**: Comprehensive testing at each phase

**Phases:**
1. **Schema Creation**: Create PostgreSQL schema
2. **Data Export**: Export MySQL data with validation
3. **UUID Generation**: Generate UUIDs for all records
4. **Relationship Mapping**: Update foreign key references
5. **Data Import**: Import data with validation
6. **Testing**: Comprehensive testing of migrated data
7. **Cutover**: Switch to PostgreSQL

## 13. Performance Considerations

### Decision: Balance normalization with performance

**Rationale:**
- **Query Performance**: Optimized for common legal software queries
- **Storage Efficiency**: Balanced normalization with practical needs
- **Migration**: Considers MySQL query patterns
- **Scalability**: Supports future growth

**Optimizations:**
- **Denormalization**: Limited denormalization for frequently accessed data
- **Materialized Views**: Consider for complex reporting queries
- **Partitioning**: Consider by organization_id for large datasets
- **Caching**: Application-level caching for expensive queries

## 14. Security Considerations

### Decision: Defense in depth approach

**Rationale:**
- **Legal Compliance**: Meets legal software security requirements
- **Data Protection**: Protects sensitive client information
- **Access Control**: Implements principle of least privilege
- **Audit Requirements**: Supports legal audit trails

**Implementation:**
- **Row Level Security**: PostgreSQL RLS for automatic filtering
- **Encryption**: Consider encryption at rest for sensitive fields
- **Audit Logging**: Track all data modifications
- **Access Control**: Role-based access control at application level

## 15. Future Extensibility

### Decision: Schema designed for future enhancements

**Rationale:**
- **Business Growth**: Accommodates new business requirements
- **Technology Evolution**: Supports new features and integrations
- **Regulatory Changes**: Adapts to changing legal requirements
- **Performance**: Supports performance optimizations

**Extensibility Features:**
- **JSONB Fields**: Flexible schema evolution
- **Modular Design**: Easy to add new entities
- **Standard Patterns**: Consistent patterns for new features
- **Migration Path**: Clear path for future schema changes