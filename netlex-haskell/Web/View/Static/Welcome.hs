module Web.View.Static.Welcome where

import Web.View.Prelude

data WelcomeView = WelcomeView 
    { users :: Int
    , organizations :: Int  
    , projects :: Int
    }

instance View WelcomeView where
    html WelcomeView { .. } = [hsx|
        <div class="hero-section bg-primary text-white py-5 mb-5">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <h1 class="display-4 fw-bold mb-4">Welcome to Netlex</h1>
                        <p class="lead mb-4">
                            A modern web application built with IHP (Integrated Haskell Platform).
                            Manage your organizations, projects, and API integrations with ease.
                        </p>
                        <div class="d-flex gap-3">
                            <a href="/Organizations" class="btn btn-light btn-lg">
                                <i class="fas fa-building me-2"></i>
                                Organizations
                            </a>
                            <a href="/Projects" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-project-diagram me-2"></i>
                                Projects
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="text-center">
                            <i class="fas fa-cube fa-10x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row mb-5">
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-users fa-3x text-primary"></i>
                            </div>
                            <h3 class="card-title">{users}</h3>
                            <p class="card-text text-muted">Registered Users</p>
                            <a href="/Users" class="btn btn-outline-primary">
                                View Users
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-building fa-3x text-success"></i>
                            </div>
                            <h3 class="card-title">{organizations}</h3>
                            <p class="card-text text-muted">Organizations</p>
                            <a href="/Organizations" class="btn btn-outline-success">
                                View Organizations
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-project-diagram fa-3x text-info"></i>
                            </div>
                            <h3 class="card-title">{projects}</h3>
                            <p class="card-text text-muted">Active Projects</p>
                            <a href="/Projects" class="btn btn-outline-info">
                                View Projects
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-rocket me-2"></i>
                                Getting Started
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>For Administrators</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            Create organizations
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            Manage user access
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            Generate API keys
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>For Users</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            Join organizations
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            Create projects
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            Use API endpoints
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-code me-2"></i>
                                API Access
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                Access our RESTful API endpoints for integration with your applications.
                            </p>
                            <div class="d-grid gap-2">
                                <a href="/api/health" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-heartbeat me-1"></i>
                                    Health Check
                                </a>
                                <a href="/ApiKeys" class="btn btn-primary btn-sm">
                                    <i class="fas fa-key me-1"></i>
                                    Manage API Keys
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    |]
