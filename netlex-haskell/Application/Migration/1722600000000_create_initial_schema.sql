-- Migration: Create initial schema
-- Created at: 2025-08-02 12:00:00 UTC

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Function for updated_at triggers
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Up migration
CREATE OR REPLACE FUNCTION up() RETURNS VOID AS $$
BEGIN
    -- Users table for authentication and user management
    CREATE TABLE users (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY NOT NULL,
        email TEXT NOT NULL UNIQUE,
        password_hash TEXT NOT NULL,
        first_name TEXT,
        last_name TEXT,
        is_active BOOLEAN DEFAULT TRUE NOT NULL,
        is_admin BOOLEAN DEFAULT FALSE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    );

    -- Sessions table for user sessions
    CREATE TABLE user_sessions (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY NOT NULL,
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        session_token TEXT NOT NULL UNIQUE,
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    );

    -- Organizations/Companies table
    CREATE TABLE organizations (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        website TEXT,
        is_active BOOLEAN DEFAULT TRUE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    );

    -- User-Organization relationships
    CREATE TABLE user_organizations (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY NOT NULL,
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
        role TEXT DEFAULT 'member' NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        UNIQUE(user_id, organization_id)
    );

    -- Projects table
    CREATE TABLE projects (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY NOT NULL,
        organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
        name TEXT NOT NULL,
        description TEXT,
        status TEXT DEFAULT 'active' NOT NULL,
        created_by UUID NOT NULL REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    );

    -- API Keys table for external integrations
    CREATE TABLE api_keys (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY NOT NULL,
        organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
        name TEXT NOT NULL,
        key_hash TEXT NOT NULL UNIQUE,
        permissions JSONB DEFAULT '{}' NOT NULL,
        is_active BOOLEAN DEFAULT TRUE NOT NULL,
        expires_at TIMESTAMP WITH TIME ZONE,
        created_by UUID NOT NULL REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        last_used_at TIMESTAMP WITH TIME ZONE
    );

    -- Audit log table
    CREATE TABLE audit_logs (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY NOT NULL,
        user_id UUID REFERENCES users(id),
        organization_id UUID REFERENCES organizations(id),
        action TEXT NOT NULL,
        resource_type TEXT,
        resource_id UUID,
        details JSONB DEFAULT '{}' NOT NULL,
        ip_address INET,
        user_agent TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
    );

    -- Indexes for better performance
    CREATE INDEX idx_users_email ON users(email);
    CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
    CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
    CREATE INDEX idx_user_organizations_user_id ON user_organizations(user_id);
    CREATE INDEX idx_user_organizations_org_id ON user_organizations(organization_id);
    CREATE INDEX idx_projects_org_id ON projects(organization_id);
    CREATE INDEX idx_api_keys_org_id ON api_keys(organization_id);
    CREATE INDEX idx_api_keys_hash ON api_keys(key_hash);
    CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
    CREATE INDEX idx_audit_logs_org_id ON audit_logs(organization_id);
    CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

    -- Triggers for updated_at
    CREATE TRIGGER set_timestamp_users
        BEFORE UPDATE ON users
        FOR EACH ROW
        EXECUTE PROCEDURE trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_organizations
        BEFORE UPDATE ON organizations
        FOR EACH ROW
        EXECUTE PROCEDURE trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_projects
        BEFORE UPDATE ON projects
        FOR EACH ROW
        EXECUTE PROCEDURE trigger_set_timestamp();
END;
$$ LANGUAGE plpgsql;

-- Down migration
CREATE OR REPLACE FUNCTION down() RETURNS VOID AS $$
BEGIN
    -- Drop triggers
    DROP TRIGGER IF EXISTS set_timestamp_users ON users;
    DROP TRIGGER IF EXISTS set_timestamp_organizations ON organizations;
    DROP TRIGGER IF EXISTS set_timestamp_projects ON projects;

    -- Drop indexes
    DROP INDEX IF EXISTS idx_users_email;
    DROP INDEX IF EXISTS idx_user_sessions_token;
    DROP INDEX IF EXISTS idx_user_sessions_user_id;
    DROP INDEX IF EXISTS idx_user_organizations_user_id;
    DROP INDEX IF EXISTS idx_user_organizations_org_id;
    DROP INDEX IF EXISTS idx_projects_org_id;
    DROP INDEX IF EXISTS idx_api_keys_org_id;
    DROP INDEX IF EXISTS idx_api_keys_hash;
    DROP INDEX IF EXISTS idx_audit_logs_user_id;
    DROP INDEX IF EXISTS idx_audit_logs_org_id;
    DROP INDEX IF EXISTS idx_audit_logs_created_at;

    -- Drop tables
    DROP TABLE IF EXISTS audit_logs;
    DROP TABLE IF EXISTS api_keys;
    DROP TABLE IF EXISTS projects;
    DROP TABLE IF EXISTS user_organizations;
    DROP TABLE IF EXISTS organizations;
    DROP TABLE IF EXISTS user_sessions;
    DROP TABLE IF EXISTS users;

    -- Drop function
    DROP FUNCTION IF EXISTS trigger_set_timestamp();
END;
$$ LANGUAGE plpgsql;