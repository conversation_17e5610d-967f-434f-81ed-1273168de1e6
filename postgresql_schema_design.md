# PostgreSQL Schema Design for Netlex3 Application

This document outlines the PostgreSQL schema design for the Netlex3 application, migrated from MySQL with improvements including UUID primary keys, enhanced constraints, and multi-tenancy support.

## Core Design Principles

1. All primary keys use UUID v4 generated on the application side
2. Foreign keys reference UUIDs for consistency
3. Multi-tenancy implemented through organization scoping
4. Improved data types and constraints over the original MySQL schema
5. JSONB fields for dynamic attributes where appropriate
6. Proper indexing for performance

## Table Definitions

### 1. Users (utente)

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    username VARCHAR(255) UNIQUE NOT NULL
        CONSTRAINT chk_username_length CHECK (LENGTH(username) >= 3),
    email VARCHAR(255) UNIQUE
        CONSTRAINT chk_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(100)
        CONSTRAINT chk_first_name_length CHECK (LENGTH(first_name) >= 1),
    last_name VARCHAR(100)
        CONSTRAINT chk_last_name_length CHECK (LENGTH(last_name) >= 1),
    role VARCHAR(50) NOT NULL
        CONSTRAINT chk_role_not_empty CHECK (LENGTH(role) >= 1),
    permissions JSONB,
    settings JSONB,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        CONSTRAINT chk_updated_after_created CHECK (updated_at >= created_at)
);

CREATE INDEX idx_users_organization_id ON users(organization_id);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);
```

### 2. Organizations (strutture, datistudio)

```sql
CREATE TABLE organizations (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL
        CONSTRAINT chk_organization_name_length CHECK (LENGTH(name) >= 1),
    legal_name VARCHAR(255),
    vat_number VARCHAR(50)
        CONSTRAINT chk_vat_number_format CHECK (vat_number ~* '^[A-Z]{2}[A-Z0-9]{5,20}$'),
    tax_code VARCHAR(50)
        CONSTRAINT chk_tax_code_format CHECK (tax_code ~* '^[A-Z0-9]{11,16}$'),
    address JSONB,
    contact_info JSONB,
    settings JSONB,
    subscription_type VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        CONSTRAINT chk_updated_after_created CHECK (updated_at >= created_at)
);

CREATE INDEX idx_organizations_name ON organizations(name);
CREATE INDEX idx_organizations_vat_number ON organizations(vat_number);
CREATE INDEX idx_organizations_tax_code ON organizations(tax_code);
CREATE INDEX idx_organizations_active ON organizations(is_active);
```

### 3. Projects (archivio)

```sql
CREATE TABLE projects (
    id UUID PRIMARY KEY,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL
        CONSTRAINT chk_title_length CHECK (LENGTH(title) >= 1),
    description TEXT,
    case_number VARCHAR(100),
    case_type_id UUID REFERENCES case_types(id),
    status_id UUID REFERENCES project_statuses(id),
    assigned_to UUID REFERENCES users(id),
    created_by UUID REFERENCES users(id),
    client_id UUID REFERENCES anagrafiche(id),
    start_date DATE,
    end_date DATE,
    estimated_completion DATE,
    budget DECIMAL(15,2) CHECK (budget >= 0),
    currency VARCHAR(3) DEFAULT 'EUR'
        CONSTRAINT chk_currency_code CHECK (currency ~* '^[A-Z]{3}$'),
    priority INTEGER DEFAULT 0 CHECK (priority >= 0 AND priority <= 10),
    tags JSONB,
    metadata JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        CONSTRAINT chk_updated_after_created CHECK (updated_at >= created_at),
    
    -- Ensure end_date is after start_date
    CONSTRAINT chk_project_dates CHECK (
        end_date IS NULL OR start_date IS NULL OR end_date >= start_date
    ),
    -- Ensure estimated_completion is after start_date
    CONSTRAINT chk_project_estimated_completion CHECK (
        estimated_completion IS NULL OR start_date IS NULL OR estimated_completion >= start_date
    )
);

CREATE INDEX idx_projects_organization_id ON projects(organization_id);
CREATE INDEX idx_projects_case_number ON projects(case_number);
CREATE INDEX idx_projects_status_id ON projects(status_id);
CREATE INDEX idx_projects_assigned_to ON projects(assigned_to);
CREATE INDEX idx_projects_client_id ON projects(client_id);
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_projects_start_date ON projects(start_date);
CREATE INDEX idx_projects_end_date ON projects(end_date);
CREATE INDEX idx_projects_active ON projects(is_active);
```

### 4. Anagrafiche (Clients/Contacts)

```sql
CREATE TABLE anagrafiche (
    id UUID PRIMARY KEY,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('person', 'company')),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    company_name VARCHAR(255),
    tax_code VARCHAR(50)
        CONSTRAINT chk_tax_code_format CHECK (
            tax_code IS NULL OR tax_code ~* '^[A-Z0-9]{11,16}$'
        ),
    vat_number VARCHAR(50)
        CONSTRAINT chk_vat_number_format CHECK (
            vat_number IS NULL OR vat_number ~* '^[A-Z]{2}[A-Z0-9]{5,20}$'
        ),
    gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'other')),
    birth_date DATE
        CONSTRAINT chk_birth_date_past CHECK (birth_date <= CURRENT_DATE),
    birth_place VARCHAR(100),
    address JSONB,
    contact_info JSONB,
    fiscal_data JSONB,
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        CONSTRAINT chk_updated_after_created CHECK (updated_at >= created_at),
    
    -- Ensure person type has first_name and last_name
    CONSTRAINT chk_person_names CHECK (
        type != 'person' OR (first_name IS NOT NULL AND last_name IS NOT NULL)
    ),
    -- Ensure company type has company_name
    CONSTRAINT chk_company_name CHECK (
        type != 'company' OR company_name IS NOT NULL
    )
);

CREATE INDEX idx_anagrafiche_organization_id ON anagrafiche(organization_id);
CREATE INDEX idx_anagrafiche_tax_code ON anagrafiche(tax_code);
CREATE INDEX idx_anagrafiche_vat_number ON anagrafiche(vat_number);
CREATE INDEX idx_anagrafiche_type ON anagrafiche(type);
CREATE INDEX idx_anagrafiche_active ON anagrafiche(is_active);
```

### 5. Project-Anagrafiche Relationship

```sql
CREATE TABLE project_anagrafiche (
    id UUID PRIMARY KEY,
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    anagrafica_id UUID NOT NULL REFERENCES anagrafiche(id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL CHECK (
        relationship_type IN ('client', 'counterparty', 'witness', 'expert', 'authority', 'other')
    ),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        CONSTRAINT chk_updated_after_created CHECK (updated_at >= created_at),
    
    UNIQUE(project_id, anagrafica_id, relationship_type)
);

CREATE INDEX idx_project_anagrafiche_project_id ON project_anagrafiche(project_id);
CREATE INDEX idx_project_anagrafiche_anagrafica_id ON project_anagrafiche(anagrafica_id);
CREATE INDEX idx_project_anagrafiche_relationship_type ON project_anagrafiche(relationship_type);
```

### 6. Documents

```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL
        CONSTRAINT chk_document_title_length CHECK (LENGTH(title) >= 1),
    description TEXT,
    category VARCHAR(100),
    file_name VARCHAR(255)
        CONSTRAINT chk_file_name_length CHECK (LENGTH(file_name) >= 1),
    file_path VARCHAR(500)
        CONSTRAINT chk_file_path_length CHECK (LENGTH(file_path) >= 1),
    file_size BIGINT CHECK (file_size >= 0),
    mime_type VARCHAR(100)
        CONSTRAINT chk_mime_type_format CHECK (mime_type ~* '^[a-z0-9\-]+/[a-z0-9\-+\.]+$'),
    protocol_number VARCHAR(100),
    protocol_date DATE,
    sender VARCHAR(255),
    recipient VARCHAR(255),
    entity VARCHAR(255),
    document_date DATE,
    metadata JSONB,
    is_archived BOOLEAN DEFAULT false,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        CONSTRAINT chk_updated_after_created CHECK (updated_at >= created_at),
    
    -- Ensure protocol_date is not in the future
    CONSTRAINT chk_protocol_date_past CHECK (protocol_date <= CURRENT_DATE),
    -- Ensure document_date is not in the future
    CONSTRAINT chk_document_date_past CHECK (document_date <= CURRENT_DATE)
);

CREATE INDEX idx_documents_organization_id ON documents(organization_id);
CREATE INDEX idx_documents_project_id ON documents(project_id);
CREATE INDEX idx_documents_protocol_number ON documents(protocol_number);
CREATE INDEX idx_documents_category ON documents(category);
CREATE INDEX idx_documents_created_by ON documents(created_by);
CREATE INDEX idx_documents_protocol_date ON documents(protocol_date);
CREATE INDEX idx_documents_document_date ON documents(document_date);
CREATE INDEX idx_documents_archived ON documents(is_archived);
```

### 7. Timesheets

```sql
CREATE TABLE timesheets (
    id UUID PRIMARY KEY,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    activity_type VARCHAR(100),
    description TEXT,
    work_date DATE NOT NULL
        CONSTRAINT chk_work_date_past CHECK (work_date <= CURRENT_DATE),
    start_time TIME,
    end_time TIME,
    duration INTERVAL,
    billable_duration INTERVAL,
    hourly_rate DECIMAL(10,2) CHECK (hourly_rate >= 0),
    amount DECIMAL(10,2) CHECK (amount >= 0),
    currency VARCHAR(3) DEFAULT 'EUR'
        CONSTRAINT chk_currency_code CHECK (currency ~* '^[A-Z]{3}$'),
    is_billed BOOLEAN DEFAULT false,
    billing_id UUID REFERENCES billing(id),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        CONSTRAINT chk_updated_after_created CHECK (updated_at >= created_at),
    
    -- Ensure end_time is after start_time
    CONSTRAINT chk_timesheet_times CHECK (
        end_time IS NULL OR start_time IS NULL OR end_time > start_time
    ),
    -- Ensure duration matches start_time and end_time
    CONSTRAINT chk_timesheet_duration CHECK (
        duration IS NULL OR start_time IS NULL OR end_time IS NULL OR
        duration = (end_time - start_time)
    )
);

CREATE INDEX idx_timesheets_organization_id ON timesheets(organization_id);
CREATE INDEX idx_timesheets_project_id ON timesheets(project_id);
CREATE INDEX idx_timesheets_user_id ON timesheets(user_id);
CREATE INDEX idx_timesheets_work_date ON timesheets(work_date);
CREATE INDEX idx_timesheets_billing_id ON timesheets(billing_id);
CREATE INDEX idx_timesheets_billed ON timesheets(is_billed);
```

### 8. Billing (Parcella)

```sql
CREATE TABLE billing (
    id UUID PRIMARY KEY,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
    client_id UUID REFERENCES anagrafiche(id),
    invoice_number VARCHAR(100),
    invoice_date DATE
        CONSTRAINT chk_invoice_date_past CHECK (invoice_date <= CURRENT_DATE),
    due_date DATE
        CONSTRAINT chk_due_date_after_invoice CHECK (
            due_date IS NULL OR invoice_date IS NULL OR due_date >= invoice_date
        ),
    amount DECIMAL(15,2) CHECK (amount >= 0),
    tax_amount DECIMAL(15,2) CHECK (tax_amount >= 0),
    total_amount DECIMAL(15,2) CHECK (total_amount >= 0),
    currency VARCHAR(3) DEFAULT 'EUR'
        CONSTRAINT chk_currency_code CHECK (currency ~* '^[A-Z]{3}$'),
    status VARCHAR(50) DEFAULT 'draft' CHECK (
        status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')
    ),
    payment_method VARCHAR(50),
    payment_date DATE
        CONSTRAINT chk_payment_date_after_invoice CHECK (
            payment_date IS NULL OR invoice_date IS NULL OR payment_date >= invoice_date
        ),
    notes TEXT,
    invoice_template VARCHAR(100),
    format_type VARCHAR(50),
    progressive_number INTEGER CHECK (progressive_number >= 0),
    register_progressive INTEGER CHECK (register_progressive >= 0),
    cash_amount DECIMAL(15,2) CHECK (cash_amount >= 0),
    metadata JSONB,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        CONSTRAINT chk_updated_after_created CHECK (updated_at >= created_at),
    
    -- Ensure total_amount equals amount + tax_amount
    CONSTRAINT chk_total_amount CHECK (total_amount = amount + tax_amount)
);

CREATE INDEX idx_billing_organization_id ON billing(organization_id);
CREATE INDEX idx_billing_project_id ON billing(project_id);
CREATE INDEX idx_billing_client_id ON billing(client_id);
CREATE INDEX idx_billing_invoice_number ON billing(invoice_number);
CREATE INDEX idx_billing_status ON billing(status);
CREATE INDEX idx_billing_created_by ON billing(created_by);
CREATE INDEX idx_billing_invoice_date ON billing(invoice_date);
CREATE INDEX idx_billing_due_date ON billing(due_date);
```

### 9. Deadlines (Scadenzario)

```sql
CREATE TABLE deadlines (
    id UUID PRIMARY KEY,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL
        CONSTRAINT chk_deadline_title_length CHECK (LENGTH(title) >= 1),
    description TEXT,
    category VARCHAR(100),
    deadline_date DATE NOT NULL,
    deadline_time TIME,
    reminder_date DATE
        CONSTRAINT chk_reminder_before_deadline CHECK (
            reminder_date IS NULL OR reminder_date <= deadline_date
        ),
    reminder_time TIME,
    status VARCHAR(50) DEFAULT 'pending' CHECK (
        status IN ('pending', 'completed', 'overdue')
    ),
    priority INTEGER DEFAULT 0 CHECK (priority >= 0 AND priority <= 10),
    assigned_to UUID REFERENCES users(id),
    created_by UUID REFERENCES users(id),
    completed_at TIMESTAMP WITH TIME ZONE
        CONSTRAINT chk_completed_before_deadline CHECK (
            completed_at IS NULL OR deadline_date IS NULL OR
            completed_at::date <= deadline_date
        ),
    notes TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        CONSTRAINT chk_updated_after_created CHECK (updated_at >= created_at)
);

CREATE INDEX idx_deadlines_organization_id ON deadlines(organization_id);
CREATE INDEX idx_deadlines_project_id ON deadlines(project_id);
CREATE INDEX idx_deadlines_deadline_date ON deadlines(deadline_date);
CREATE INDEX idx_deadlines_status ON deadlines(status);
CREATE INDEX idx_deadlines_assigned_to ON deadlines(assigned_to);
CREATE INDEX idx_deadlines_created_by ON deadlines(created_by);
CREATE INDEX idx_deadlines_priority ON deadlines(priority);
```

### 10. Appointments (Agenda)

```sql
CREATE TABLE appointments (
    id UUID PRIMARY KEY,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL
        CONSTRAINT chk_appointment_title_length CHECK (LENGTH(title) >= 1),
    description TEXT,
    appointment_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    location VARCHAR(255),
    authority VARCHAR(255),
    judge VARCHAR(255),
    participants JSONB,
    status VARCHAR(50) DEFAULT 'scheduled' CHECK (
        status IN ('scheduled', 'completed', 'cancelled')
    ),
    reminder_sent BOOLEAN DEFAULT false,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        CONSTRAINT chk_updated_after_created CHECK (updated_at >= created_at),
    
    -- Ensure end_time is after start_time
    CONSTRAINT chk_appointment_times CHECK (end_time > start_time)
);

CREATE INDEX idx_appointments_organization_id ON appointments(organization_id);
CREATE INDEX idx_appointments_project_id ON appointments(project_id);
CREATE INDEX idx_appointments_appointment_date ON appointments(appointment_date);
CREATE INDEX idx_appointments_status ON appointments(status);
CREATE INDEX idx_appointments_created_by ON appointments(created_by);
CREATE INDEX idx_appointments_reminder_sent ON appointments(reminder_sent);
```

### 11. PCT Registries (Processo Telematico)

```sql
-- Main PCT registry table
CREATE TABLE pct_registries (
    id UUID PRIMARY KEY,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    registry_type VARCHAR(50) NOT NULL CHECK (
        registry_type IN ('civil', 'criminal', 'administrative', 'minors', 'unep', 'other')
    ),
    register_id UUID REFERENCES pct_registers(id),
    role_id UUID REFERENCES pct_roles(id),
    pwid VARCHAR(20)
        CONSTRAINT chk_pwid_format CHECK (pwid ~* '^[A-Z0-9]{1,20}$'),
    case_number VARCHAR(100),
    subject_id UUID REFERENCES pct_subjects(id),
    rite_id UUID REFERENCES pct_rites(id),
    authority_id UUID REFERENCES pct_authorities(id),
    office_code VARCHAR(20),
    registration_date DATE
        CONSTRAINT chk_registration_date_past CHECK (registration_date <= CURRENT_DATE),
    status VARCHAR(50) DEFAULT 'active' CHECK (
        status IN ('active', 'closed', 'suspended', 'archived')
    ),
    metadata JSONB,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        CONSTRAINT chk_updated_after_created CHECK (updated_at >= created_at)
);

CREATE INDEX idx_pct_registries_organization_id ON pct_registries(organization_id);
CREATE INDEX idx_pct_registries_project_id ON pct_registries(project_id);
CREATE INDEX idx_pct_registries_case_number ON pct_registries(case_number);
CREATE INDEX idx_pct_registries_pwid ON pct_registries(pwid);
CREATE INDEX idx_pct_registries_created_by ON pct_registries(created_by);
CREATE INDEX idx_pct_registries_registry_type ON pct_registries(registry_type);
CREATE INDEX idx_pct_registries_status ON pct_registries(status);

-- PCT Registers
CREATE TABLE pct_registers (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL
        CONSTRAINT chk_register_name_length CHECK (LENGTH(name) >= 1),
    code VARCHAR(20) UNIQUE NOT NULL
        CONSTRAINT chk_register_code_format CHECK (code ~* '^[A-Z0-9_]{1,20}$'),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- PCT Roles
CREATE TABLE pct_roles (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL
        CONSTRAINT chk_role_name_length CHECK (LENGTH(name) >= 1),
    code VARCHAR(20) UNIQUE NOT NULL
        CONSTRAINT chk_role_code_format CHECK (code ~* '^[A-Z0-9_]{1,20}$'),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- PCT Subjects
CREATE TABLE pct_subjects (
    id UUID PRIMARY KEY,
    code VARCHAR(20) UNIQUE NOT NULL
        CONSTRAINT chk_subject_code_format CHECK (code ~* '^[A-Z0-9_]{1,20}$'),
    name VARCHAR(255) NOT NULL
        CONSTRAINT chk_subject_name_length CHECK (LENGTH(name) >= 1),
    role_id UUID REFERENCES pct_roles(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- PCT Rites
CREATE TABLE pct_rites (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL
        CONSTRAINT chk_rite_name_length CHECK (LENGTH(name) >= 1),
    code VARCHAR(50) UNIQUE NOT NULL
        CONSTRAINT chk_rite_code_format CHECK (code ~* '^[A-Z0-9_]{1,50}$'),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- PCT Authorities
CREATE TABLE pct_authorities (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL
        CONSTRAINT chk_authority_name_length CHECK (LENGTH(name) >= 1),
    email VARCHAR(255)
        CONSTRAINT chk_authority_email_format CHECK (
            email IS NULL OR email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
        ),
    pec VARCHAR(255)  -- Certified email
        CONSTRAINT chk_pec_format CHECK (
            pec IS NULL OR pec ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
        ),
    district VARCHAR(10)
        CONSTRAINT chk_district_format CHECK (district ~* '^GL[A-Z]{2}$'),
    pwid VARCHAR(20)
        CONSTRAINT chk_authority_pwid_format CHECK (pwid ~* '^[0-9]{11}$'),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- PCT Acts
CREATE TABLE pct_acts (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL
        CONSTRAINT chk_act_name_length CHECK (LENGTH(name) >= 1),
    configuration VARCHAR(100),
    is_cartabia BOOLEAN DEFAULT false,
    schema_name VARCHAR(100),
    is_published BOOLEAN DEFAULT true,
    is_beta BOOLEAN DEFAULT false,
    note_on_role_registration BOOLEAN DEFAULT false,
    rg_mandatory BOOLEAN DEFAULT false,
    is_favorite BOOLEAN DEFAULT false,
    tags TEXT,
    unique_id VARCHAR(100)
        CONSTRAINT chk_unique_id_format CHECK (unique_id ~* '^F[A-Z0-9]{10,100}$'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- PCT Registry-Act relationship
CREATE TABLE pct_registry_acts (
    id UUID PRIMARY KEY,
    registry_id UUID NOT NULL REFERENCES pct_registries(id) ON DELETE CASCADE,
    act_id UUID NOT NULL REFERENCES pct_acts(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(registry_id, act_id)
);

CREATE INDEX idx_pct_registry_acts_registry_id ON pct_registry_acts(registry_id);
CREATE INDEX idx_pct_registry_acts_act_id ON pct_registry_acts(act_id);
```

### 12. Lookup Tables

```sql
-- Project statuses
CREATE TABLE project_statuses (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL
        CONSTRAINT chk_status_name_length CHECK (LENGTH(name) >= 1),
    description TEXT,
    color VARCHAR(20)
        CONSTRAINT chk_color_format CHECK (color ~* '^#[A-F0-9]{6}$'),
    order_index INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Case types
CREATE TABLE case_types (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL
        CONSTRAINT chk_case_type_name_length CHECK (LENGTH(name) >= 1),
    description TEXT,
    category VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Document categories
CREATE TABLE document_categories (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL
        CONSTRAINT chk_document_category_name_length CHECK (LENGTH(name) >= 1),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Deadline categories
CREATE TABLE deadline_categories (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL
        CONSTRAINT chk_deadline_category_name_length CHECK (LENGTH(name) >= 1),
    description TEXT,
    color VARCHAR(20)
        CONSTRAINT chk_deadline_category_color_format CHECK (color ~* '^#[A-F0-9]{6}$'),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Multi-Tenancy Implementation

All tables include an `organization_id` field that references the organizations table. This implements row-level security for multi-tenancy, ensuring that users can only access data belonging to their organization.

## Key Design Improvements

1. **UUID Primary Keys**: All tables use UUID v4 as primary keys for better scalability and to avoid sequential ID issues
2. **Enhanced Constraints**: 
   - Check constraints for data validation (email format, VAT number format, etc.)
   - Cross-field validation (end_date after start_date, etc.)
   - Data type specific constraints (positive amounts, valid currency codes, etc.)
3. **JSONB Fields**: Used for flexible data storage of address, contact info, metadata, and settings
4. **Proper Indexing**: Strategic indexes on foreign keys and commonly queried fields
5. **Multi-Tenancy**: Organization scoping implemented consistently across all tables
6. **Timestamps**: All tables include created_at and updated_at timestamps with timezone support
7. **Referential Integrity**: Proper foreign key constraints with appropriate ON DELETE actions

## Data Migration Considerations

1. UUIDs will need to be generated for all existing records during migration
2. Foreign key references will need to be updated to use UUIDs instead of integer IDs
3. Multi-tenancy will need to be implemented by associating existing records with appropriate organizations
4. JSON fields will need to be populated with structured data from existing text fields where applicable
5. Data validation constraints may require cleaning existing data before migration
6. Date validation constraints may require adjusting future-dated records