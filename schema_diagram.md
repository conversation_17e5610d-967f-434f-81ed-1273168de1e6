# Netlex3 PostgreSQL Schema Diagram

This diagram shows the relationships between tables in the Netlex3 PostgreSQL database schema.

```mermaid
erDiagram
    organizations ||--o{ users : "has"
    organizations ||--o{ projects : "has"
    organizations ||--o{ anagrafiche : "has"
    organizations ||--o{ documents : "has"
    organizations ||--o{ timesheets : "has"
    organizations ||--o{ billing : "has"
    organizations ||--o{ deadlines : "has"
    organizations ||--o{ appointments : "has"
    organizations ||--o{ pct_registries : "has"
    
    users ||--o{ projects : "creates"
    users ||--o{ projects : "assigned"
    users ||--o{ documents : "creates"
    users ||--o{ timesheets : "logs"
    users ||--o{ billing : "creates"
    users ||--o{ deadlines : "creates"
    users ||--o{ deadlines : "assigned"
    users ||--o{ appointments : "creates"
    users ||--o{ pct_registries : "creates"
    
    anagrafiche ||--o{ projects : "client"
    anagrafiche ||--o{ billing : "client"
    
    projects ||--o{ project_anagrafiche : "has"
    project_anagrafiche }o--|| anagrafiche : "relates"
    
    projects ||--o{ documents : "has"
    projects ||--o{ timesheets : "has"
    projects ||--o{ billing : "has"
    projects ||--o{ deadlines : "has"
    projects ||--o{ appointments : "has"
    projects ||--o{ pct_registries : "has"
    
    case_types ||--o{ projects : "categorizes"
    project_statuses ||--o{ projects : "status"
    
    billing ||--o{ timesheets : "bills"
    
    deadline_categories ||--o{ deadlines : "categorizes"
    
    pct_registers ||--o{ pct_registries : "register"
    pct_roles ||--o{ pct_registries : "role"
    pct_roles ||--o{ pct_subjects : "role"
    pct_subjects ||--o{ pct_registries : "subject"
    pct_rites ||--o{ pct_registries : "rite"
    pct_authorities ||--o{ pct_registries : "authority"
    
    pct_registries ||--o{ pct_registry_acts : "has"
    pct_registry_acts }o--|| pct_acts : "act"
    
    document_categories ||--o{ documents : "categorizes"
```

## Table Relationship Explanations

### Core Entities

1. **Organizations** - The central entity for multi-tenancy. All other entities belong to an organization.

2. **Users** - System users who belong to an organization and can create/modify other entities.

3. **Projects (Archivio)** - Legal cases or projects that belong to an organization and can have multiple clients, documents, timesheets, etc.

4. **Anagrafiche** - Clients/contacts that can be associated with projects and billing.

5. **Documents** - Files related to projects, with metadata like protocol numbers and dates.

6. **Timesheets** - Time tracking entries that can be associated with projects and billed.

7. **Billing** - Invoices and billing information for clients.

8. **Deadlines** - Important dates and deadlines related to projects.

9. **Appointments** - Scheduled meetings or court dates related to projects.

10. **PCT Registries** - Processo Telematico (electronic legal process) registries with detailed information about legal proceedings.

### Relationship Details

- **One-to-Many**: Organizations have many users, projects, anagrafiche, etc.
- **Many-to-Many**: Projects and anagrafiche are connected through the project_anagrafiche junction table to support multiple relationship types.
- **Lookup Tables**: Case types, project statuses, document categories, etc. provide standardized values.
- **PCT Relationships**: PCT registries have complex relationships with registers, roles, subjects, rites, authorities, and acts.