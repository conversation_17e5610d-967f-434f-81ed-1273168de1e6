resolver: lts-22.0

packages:
- .

extra-deps:
- git: https://github.com/digitallyinduced/ihp.git
  commit: 13c459d0d5111ae274acf322b3617daa5815e877
  subdirs:
    - .
    - ihp-hsx
    - ihp-postgresql-simple-extra
- countable-inflections-0.3.0@sha256:1ae91292663c99b655db47e534964f0041e16d3f4ff530e01a752b09bcad9fbe,1553
- minio-hs-1.7.0@sha256:b2721568f2cb8d3cb8ff14eb6ee25af15c164f049493c65664bb2a2d1dd9a071,11708
- random-strings-0.1.1.0@sha256:935a7a23dab45411960df77636a29b44ce42b89eeb15f2b1e809d771491fa677,2517
- smtp-mail-0.5.0.0@sha256:e379788a4e649fa0c3233774501a103bc8c87f789ff4425c5282e4939fcc4b56,1468
- typerep-map-0.6.0.0@sha256:e54a4e99614ad890a10200da687efa189860c13207a97063db1b6881032ebecc,4890
- wai-session-clientsession-0.1@sha256:e5778e6a7bc67d2e91f44d9efe8de24cfb4181d3c5445195d0cc7167fbc6ad24,1223
- wai-util-0.8@sha256:7181e748ccd405ecef5a586c954acb3d62b55b8eb7368efb5d80d6bf38e8211b,1260
- connection-0.3.1@sha256:3dcbf2f10d1677d339267d6dacb38a24913622edf42b05f75a73f381610ddbfc,1563
- http-accept-0.2@sha256:c55935f6ff4984aee96ebc130cc9aa6919d322becb613c049a81203cec9fe65e,1154
- warp-systemd-0.3.0.0@sha256:0d3d6d617b507fe6dfd9278374bbb9abf183987f96e6194b90972ebfddc365e5,1272

allow-newer: true

ghc-options:
  "$everything": -haddock

extra-include-dirs:
- /usr/include/postgresql
- /usr/local/include

extra-lib-dirs:
- /usr/lib/x86_64-linux-gnu
- /usr/local/lib
